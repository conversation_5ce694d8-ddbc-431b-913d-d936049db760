const express = require('express');
const cors = require('cors');
const multer = require('multer');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3001;

// 数据库文件路径
const DB_PATH = path.join(__dirname, 'database.sqlite');
// 上传文件存储路径
const UPLOADS_DIR = path.join(__dirname, 'uploads');
const MODELS_DIR = path.join(UPLOADS_DIR, 'models');
const THUMBNAILS_DIR = path.join(UPLOADS_DIR, 'thumbnails');

// 确保上传目录存在
fs.mkdirSync(MODELS_DIR, { recursive: true });
fs.mkdirSync(THUMBNAILS_DIR, { recursive: true });

// 中间件
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(UPLOADS_DIR)); // 开放uploads目录的静态访问

// 数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('Error opening database', err.message);
  } else {
    console.log('Connected to the SQLite database.');
    // 创建表（如果不存在）
    db.serialize(() => {
      db.run(`CREATE TABLE IF NOT EXISTS models (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        thumbnail TEXT, 
        fileType TEXT,
        size TEXT,
        createdAt TEXT,
        filePath TEXT
      )`);
      db.run(`CREATE TABLE IF NOT EXISTS materials (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        thumbnail TEXT,
        color TEXT,
        metalness REAL,
        roughness REAL,
        glass REAL,
        createdAt TEXT
      )`);
    });
  }
});

// Multer 配置 (文件上传)
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    if (file.fieldname === 'modelFile') {
      cb(null, MODELS_DIR);
    } else if (file.fieldname === 'thumbnailFile') {
      cb(null, THUMBNAILS_DIR);
    }
  },
  filename: function (req, file, cb) {
    cb(null, uuidv4() + path.extname(file.originalname)); // 使用uuid确保文件名唯一
  }
});
const upload = multer({ storage: storage });

// --- API 路由 ---

// 模型 API
// GET /api/models - 获取所有模型
app.get('/api/models', (req, res) => {
  db.all('SELECT * FROM models ORDER BY createdAt DESC', [], (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

// POST /api/models - 上传新模型
app.post('/api/models', upload.fields([{ name: 'modelFile', maxCount: 1 }, { name: 'thumbnailFile', maxCount: 1 }]), (req, res) => {
  const { name, fileType, size } = req.body;
  const id = uuidv4();
  const createdAt = new Date().toISOString();
  
  const modelFile = req.files && req.files['modelFile'] ? req.files['modelFile'][0] : null;
  const thumbnailFile = req.files && req.files['thumbnailFile'] ? req.files['thumbnailFile'][0] : null;

  if (!name || !modelFile) {
    return res.status(400).json({ error: 'Model name and file are required.' });
  }

  const filePath = modelFile ? `/uploads/models/${modelFile.filename}` : null;
  const thumbnailPath = thumbnailFile ? `/uploads/thumbnails/${thumbnailFile.filename}` : null;

  const sql = `INSERT INTO models (id, name, thumbnail, fileType, size, createdAt, filePath) VALUES (?, ?, ?, ?, ?, ?, ?)`;
  const params = [id, name, thumbnailPath, fileType || 'GLB', size || 'N/A', createdAt, filePath];

  db.run(sql, params, function (err) {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.status(201).json({ id, name, thumbnail: thumbnailPath, fileType, size, createdAt, filePath });
  });
});

// DELETE /api/models/:id - 删除模型
app.delete('/api/models/:id', (req, res) => {
  const { id } = req.params;
  // 首先查询模型以获取文件路径
  db.get('SELECT filePath, thumbnail FROM models WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!row) {
      return res.status(404).json({ error: 'Model not found' });
    }

    // 删除数据库记录
    db.run('DELETE FROM models WHERE id = ?', id, function (err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Model not found or already deleted' });
      }
      // 删除文件
      if (row.filePath) {
        fs.unlink(path.join(__dirname, row.filePath), (unlinkErr) => {
          if (unlinkErr) console.error('Error deleting model file:', unlinkErr);
        });
      }
      if (row.thumbnail) {
        fs.unlink(path.join(__dirname, row.thumbnail), (unlinkErr) => {
          if (unlinkErr) console.error('Error deleting thumbnail file:', unlinkErr);
        });
      }
      res.status(200).json({ message: 'Model deleted successfully' });
    });
  });
});

// 材质 API (后续实现)
app.get('/api/materials', (req, res) => {
  db.all('SELECT * FROM materials ORDER BY createdAt DESC', [], (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

app.post('/api/materials', upload.single('thumbnailFile'), (req, res) => {
  const { name, color, metalness, roughness, glass } = req.body;
  const id = uuidv4();
  const createdAt = new Date().toISOString();
  const thumbnailPath = req.file ? `/uploads/thumbnails/${req.file.filename}` : null;

  if (!name || !color) {
    return res.status(400).json({ error: 'Material name and color are required.' });
  }

  const sql = `INSERT INTO materials (id, name, thumbnail, color, metalness, roughness, glass, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
  const params = [id, name, thumbnailPath, color, parseFloat(metalness) || 0, parseFloat(roughness) || 0.5, parseFloat(glass) || 0, createdAt];

  db.run(sql, params, function (err) {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.status(201).json({ id, name, thumbnail: thumbnailPath, color, metalness, roughness, glass, createdAt });
  });
});

app.delete('/api/materials/:id', (req, res) => {
  const { id } = req.params;
  db.get('SELECT thumbnail FROM materials WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    db.run('DELETE FROM materials WHERE id = ?', id, function (err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Material not found or already deleted' });
      }
      if (row && row.thumbnail) {
        fs.unlink(path.join(__dirname, row.thumbnail), (unlinkErr) => {
          if (unlinkErr) console.error('Error deleting material thumbnail file:', unlinkErr);
        });
      }
      res.status(200).json({ message: 'Material deleted successfully' });
    });
  });
});


// 启动服务器
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});

// 关闭数据库连接
process.on('SIGINT', () => {
  db.close((err) => {
    if (err) {
      return console.error(err.message);
    }
    console.log('Closed the database connection.');
    process.exit(0);
  });
});
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-dialog {
  width: 420px;
  background: var(--color-bg-dialog);
  border-radius: var(--radius-l);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  padding: 16px;
  color: var(--color-content-regular);
}

.modal-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-m);
  margin-bottom: 16px;
}
.close-icon {
  cursor: pointer;
}

.upload-area {
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
}
.upload-area.drag-over {
  background-color: rgba(77, 171, 247, 0.1);
}
.upload-area p {
  margin: 8px 0 0;
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}
.file-name {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

@import './styles/variables.css';

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: var(--font-size);
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: var(--font-size);
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* === App-specific styles (merged from App.css) === */

/* 主题样式 */
.app-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden;
}

.app-container.theme-dark {
  background-color: var(--color-page-background);
  color: var(--color-content-accent);
}

.app-container.theme-light {
  background-color: var(--color-page-background);
  color: var(--color-content-accent);
}

/* 头部样式 */
header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.app-container.light header {
  border-bottom-color: var(--color-border);
}

/* 展示区域样式 */
.demo-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
}

.dark .demo-section {
  background-color: rgba(255, 255, 255, 0.05);
}

.light .demo-section {
  background-color: rgba(0, 0, 0, 0.03);
}

.demo-section h2 {
  margin-bottom: 1rem;
  font-size: var(--font-size);
  font-weight: 500;
}

.demo-section h3 {
  margin-bottom: 0.5rem;
  font-size: var(--font-size);
  font-weight: 500;
  opacity: 0.85;
}

/* 滑块演示区域样式 */
.slider-demo {
  margin-bottom: 1.5rem;
  max-width: 500px;
}

.volume-slider {
  display: flex;
  align-items: center;
  gap: 16px;
}

.volume-slider svg {
  color: var(--color-content-accent);
}

/* 搜索框演示区域样式 */
.search-demo {
  margin-bottom: 1.5rem;
  max-width: 500px;
}

.search-demo h3 {
  margin-bottom: 12px;
}

.buttons-row {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* 计数器样式 */
.counter-demo {
  display: flex;
  align-items: center;
  gap: 1rem;
}
